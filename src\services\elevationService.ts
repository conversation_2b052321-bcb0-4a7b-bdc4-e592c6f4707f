/**
 * Elevation Service for Viewshed Analysis
 * Provides elevation data from multiple sources for terrain-based line-of-sight calculations
 */

export interface ElevationPoint {
  latitude: number;
  longitude: number;
  elevation: number; // meters above sea level
}

export interface ElevationProfile {
  points: ElevationPoint[];
  distance: number; // total distance in meters
  resolution: number; // meters between points
}

export interface ViewshedResult {
  visible: boolean;
  distance: number;
  elevation: number;
  obstruction?: {
    distance: number;
    elevation: number;
    height: number;
  };
}

/**
 * Elevation Service Class
 */
export class ElevationService {
  private elevationCache = new Map<string, number>();
  private readonly CACHE_EXPIRY = 1000 * 60 * 60; // 1 hour
  private cacheTimestamps = new Map<string, number>();

  /**
   * Get elevation for a single point
   */
  async getElevation(
    latitude: number, 
    longitude: number, 
    source: 'mapbox' | 'open-elevation' = 'mapbox'
  ): Promise<number> {
    const cacheKey = `${latitude.toFixed(6)},${longitude.toFixed(6)},${source}`;
    
    // Check cache first
    if (this.elevationCache.has(cacheKey)) {
      const timestamp = this.cacheTimestamps.get(cacheKey) || 0;
      if (Date.now() - timestamp < this.CACHE_EXPIRY) {
        return this.elevationCache.get(cacheKey)!;
      }
    }

    try {
      let elevation: number;
      
      if (source === 'mapbox') {
        elevation = await this.getMapboxElevation(latitude, longitude);
      } else {
        elevation = await this.getOpenElevation(latitude, longitude);
      }

      // Cache the result
      this.elevationCache.set(cacheKey, elevation);
      this.cacheTimestamps.set(cacheKey, Date.now());
      
      return elevation;
    } catch (error) {
      console.warn('Failed to get elevation data:', error);
      return 0; // Default to sea level
    }
  }

  /**
   * Get elevation profile between two points
   */
  async getElevationProfile(
    startLat: number,
    startLng: number,
    endLat: number,
    endLng: number,
    resolution: number = 100, // meters between sample points
    source: 'mapbox' | 'open-elevation' = 'mapbox'
  ): Promise<ElevationProfile> {
    const distance = this.calculateDistance(startLat, startLng, endLat, endLng);
    const numPoints = Math.max(2, Math.ceil(distance / resolution));
    const points: ElevationPoint[] = [];

    for (let i = 0; i < numPoints; i++) {
      const ratio = i / (numPoints - 1);
      const lat = startLat + (endLat - startLat) * ratio;
      const lng = startLng + (endLng - startLng) * ratio;
      
      const elevation = await this.getElevation(lat, lng, source);
      points.push({ latitude: lat, longitude: lng, elevation });
    }

    return {
      points,
      distance,
      resolution: distance / (numPoints - 1)
    };
  }

  /**
   * Calculate line-of-sight visibility between two points
   */
  async calculateLineOfSight(
    observerLat: number,
    observerLng: number,
    observerHeight: number,
    targetLat: number,
    targetLng: number,
    targetHeight: number,
    source: 'mapbox' | 'open-elevation' = 'mapbox'
  ): Promise<ViewshedResult> {
    const profile = await this.getElevationProfile(
      observerLat, observerLng, targetLat, targetLng, 50, source
    );

    if (profile.points.length < 2) {
      return { visible: false, distance: 0, elevation: 0 };
    }

    const observerElevation = profile.points[0].elevation + observerHeight;
    const targetElevation = profile.points[profile.points.length - 1].elevation + targetHeight;
    const totalDistance = profile.distance;

    // Check line-of-sight along the profile
    for (let i = 1; i < profile.points.length - 1; i++) {
      const point = profile.points[i];
      const distanceFromObserver = this.calculateDistance(
        observerLat, observerLng, point.latitude, point.longitude
      );
      
      // Calculate required elevation for line-of-sight at this distance
      const ratio = distanceFromObserver / totalDistance;
      const requiredElevation = observerElevation + (targetElevation - observerElevation) * ratio;
      
      // Check if terrain blocks the line-of-sight
      if (point.elevation > requiredElevation) {
        return {
          visible: false,
          distance: distanceFromObserver,
          elevation: point.elevation,
          obstruction: {
            distance: distanceFromObserver,
            elevation: point.elevation,
            height: point.elevation - requiredElevation
          }
        };
      }
    }

    return {
      visible: true,
      distance: totalDistance,
      elevation: targetElevation
    };
  }

  /**
   * Get elevation from Mapbox Tilequery API
   */
  private async getMapboxElevation(latitude: number, longitude: number): Promise<number> {
    // Note: This requires a Mapbox access token
    // For demo purposes, we'll simulate elevation data
    // In production, you would use: https://api.mapbox.com/v4/mapbox.mapbox-terrain-v2/tilequery/{longitude},{latitude}.json
    
    // Simulate elevation based on latitude (rough approximation)
    const baseElevation = Math.max(0, (Math.abs(latitude - 30) * 50) + (Math.random() * 200 - 100));
    return Math.round(baseElevation);
  }

  /**
   * Get elevation from Open Elevation API
   */
  private async getOpenElevation(latitude: number, longitude: number): Promise<number> {
    try {
      const response = await fetch(
        `https://api.open-elevation.com/api/v1/lookup?locations=${latitude},${longitude}`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      
      const data = await response.json();
      return data.results[0]?.elevation || 0;
    } catch (error) {
      console.warn('Open Elevation API failed:', error);
      // Fallback to simulated elevation
      return this.getMapboxElevation(latitude, longitude);
    }
  }

  /**
   * Calculate distance between two points in meters
   */
  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371000; // Earth's radius in meters
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Clear elevation cache
   */
  clearCache(): void {
    this.elevationCache.clear();
    this.cacheTimestamps.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; hitRate: number } {
    return {
      size: this.elevationCache.size,
      hitRate: 0 // TODO: Implement hit rate tracking
    };
  }
}

// Export singleton instance
export const elevationService = new ElevationService();
