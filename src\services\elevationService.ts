/**
 * Elevation Service for Viewshed Analysis
 * Provides elevation data from multiple sources for terrain-based line-of-sight calculations
 */

export interface ElevationPoint {
  latitude: number;
  longitude: number;
  elevation: number; // meters above sea level
}

export interface ElevationProfile {
  points: ElevationPoint[];
  distance: number; // total distance in meters
  resolution: number; // meters between points
}

export interface ViewshedResult {
  visible: boolean;
  distance: number;
  elevation: number;
  obstruction?: {
    distance: number;
    elevation: number;
    height: number;
  };
}

/**
 * Elevation Service Class
 */
export class ElevationService {
  private elevationCache = new Map<string, number>();
  private readonly CACHE_EXPIRY = 1000 * 60 * 60; // 1 hour
  private cacheTimestamps = new Map<string, number>();

  /**
   * Get elevation for a single point
   */
  async getElevation(
    latitude: number,
    longitude: number,
    source: 'mapbox' | 'open-elevation' = 'open-elevation'
  ): Promise<number> {
    const cacheKey = `${latitude.toFixed(6)},${longitude.toFixed(6)},${source}`;

    // Check cache first
    if (this.elevationCache.has(cacheKey)) {
      const timestamp = this.cacheTimestamps.get(cacheKey) || 0;
      if (Date.now() - timestamp < this.CACHE_EXPIRY) {
        const cachedElevation = this.elevationCache.get(cacheKey)!;
        console.log(`📦 Using cached elevation: ${cachedElevation}m for ${latitude}, ${longitude}`);
        return cachedElevation;
      }
    }

    console.log(`🔍 Getting elevation for ${latitude}, ${longitude} using ${source}`);

    try {
      let elevation: number;

      if (source === 'open-elevation') {
        elevation = await this.getOpenElevation(latitude, longitude);
      } else {
        elevation = await this.getMapboxElevation(latitude, longitude);
      }

      // Cache the result
      this.elevationCache.set(cacheKey, elevation);
      this.cacheTimestamps.set(cacheKey, Date.now());

      console.log(`💾 Cached elevation: ${elevation}m for ${latitude}, ${longitude}`);
      return elevation;
    } catch (error) {
      console.error('❌ Failed to get elevation data:', error);
      console.log('🌊 Defaulting to sea level (0m)');
      return 0; // Default to sea level
    }
  }

  /**
   * Get elevation profile between two points
   */
  async getElevationProfile(
    startLat: number,
    startLng: number,
    endLat: number,
    endLng: number,
    resolution: number = 100, // meters between sample points
    source: 'mapbox' | 'open-elevation' = 'mapbox'
  ): Promise<ElevationProfile> {
    const distance = this.calculateDistance(startLat, startLng, endLat, endLng);
    const numPoints = Math.max(2, Math.ceil(distance / resolution));
    const points: ElevationPoint[] = [];

    for (let i = 0; i < numPoints; i++) {
      const ratio = i / (numPoints - 1);
      const lat = startLat + (endLat - startLat) * ratio;
      const lng = startLng + (endLng - startLng) * ratio;

      const elevation = await this.getElevation(lat, lng, source);
      points.push({ latitude: lat, longitude: lng, elevation });
    }

    return {
      points,
      distance,
      resolution: distance / (numPoints - 1)
    };
  }

  /**
   * Calculate line-of-sight visibility between two points
   */
  async calculateLineOfSight(
    observerLat: number,
    observerLng: number,
    observerHeight: number,
    targetLat: number,
    targetLng: number,
    targetHeight: number,
    source: 'mapbox' | 'open-elevation' = 'mapbox'
  ): Promise<ViewshedResult> {
    const profile = await this.getElevationProfile(
      observerLat, observerLng, targetLat, targetLng, 50, source
    );

    if (profile.points.length < 2) {
      return { visible: false, distance: 0, elevation: 0 };
    }

    const observerElevation = profile.points[0].elevation + observerHeight;
    const targetElevation = profile.points[profile.points.length - 1].elevation + targetHeight;
    const totalDistance = profile.distance;

    // Check line-of-sight along the profile
    for (let i = 1; i < profile.points.length - 1; i++) {
      const point = profile.points[i];
      const distanceFromObserver = this.calculateDistance(
        observerLat, observerLng, point.latitude, point.longitude
      );

      // Calculate required elevation for line-of-sight at this distance
      const ratio = distanceFromObserver / totalDistance;
      const requiredElevation = observerElevation + (targetElevation - observerElevation) * ratio;

      // Check if terrain blocks the line-of-sight
      if (point.elevation > requiredElevation) {
        return {
          visible: false,
          distance: distanceFromObserver,
          elevation: point.elevation,
          obstruction: {
            distance: distanceFromObserver,
            elevation: point.elevation,
            height: point.elevation - requiredElevation
          }
        };
      }
    }

    return {
      visible: true,
      distance: totalDistance,
      elevation: targetElevation
    };
  }

  /**
   * Get elevation from Mapbox Tilequery API (simulated for demo)
   */
  private async getMapboxElevation(latitude: number, longitude: number): Promise<number> {
    // Use the same realistic simulation as the fallback
    return this.getRealisticElevation(latitude, longitude);
  }

  /**
   * Get elevation from real elevation APIs
   */
  private async getOpenElevation(latitude: number, longitude: number): Promise<number> {
    console.log(`🏔️ Fetching REAL elevation data for ${latitude}, ${longitude}`);

    try {
      // Primary: Open-Meteo Elevation API (most reliable, no CORS)
      const response = await fetch(
        `https://api.open-meteo.com/v1/elevation?latitude=${latitude}&longitude=${longitude}`,
        {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          }
        }
      );

      if (response.ok) {
        const data = await response.json();

        if (data.elevation && Array.isArray(data.elevation) && data.elevation.length > 0) {
          const elevation = Math.round(data.elevation[0]);
          console.log(`✅ Real elevation data: ${elevation}m`);
          return elevation;
        }
      }

      throw new Error(`Open-Meteo API failed: ${response.status}`);

    } catch (error) {
      console.warn('❌ Real elevation API failed, using geographic simulation:', error);

      // Fallback to geographic-based simulation (clearly marked as simulation)
      const simulatedElevation = await this.getRealisticElevation(latitude, longitude);
      console.log(`⚠️ Using SIMULATED elevation: ${simulatedElevation}m`);
      return simulatedElevation;
    }
  }

  /**
   * Get realistic elevation simulation based on geographic patterns
   */
  private getRealisticElevation(latitude: number, longitude: number): Promise<number> {
    // Use deterministic elevation based on actual geographic patterns
    // This is much more realistic than random data

    // Major mountain ranges and geographic features
    const mountainRanges = [
      { lat: 28, lng: 84, elevation: 8000, radius: 5 }, // Himalayas
      { lat: 46, lng: 8, elevation: 4000, radius: 3 },  // Alps
      { lat: 40, lng: -106, elevation: 4000, radius: 4 }, // Rocky Mountains
      { lat: -22, lng: -68, elevation: 6000, radius: 3 }, // Andes
    ];

    let baseElevation = 0;

    // Check proximity to major mountain ranges
    for (const range of mountainRanges) {
      const distance = Math.sqrt(
        Math.pow(latitude - range.lat, 2) + Math.pow(longitude - range.lng, 2)
      );

      if (distance < range.radius) {
        const factor = 1 - (distance / range.radius);
        baseElevation = Math.max(baseElevation, range.elevation * factor);
      }
    }

    // Add coastal effects (lower elevation near coasts)
    const coastalFactor = Math.min(
      Math.abs(latitude) / 90, // Distance from equator
      Math.abs(Math.abs(longitude) - 180) / 180 // Distance from antimeridian
    );

    // Add terrain variation based on coordinates
    const terrainNoise =
      Math.sin(latitude * 0.1) * Math.cos(longitude * 0.1) * 200 +
      Math.sin(latitude * 0.05) * Math.cos(longitude * 0.05) * 500;

    const finalElevation = Math.max(0, baseElevation + terrainNoise * coastalFactor);

    return Promise.resolve(Math.round(finalElevation));
  }

  /**
   * Calculate distance between two points in meters
   */
  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371000; // Earth's radius in meters
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Clear elevation cache
   */
  clearCache(): void {
    this.elevationCache.clear();
    this.cacheTimestamps.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; hitRate: number } {
    return {
      size: this.elevationCache.size,
      hitRate: 0 // TODO: Implement hit rate tracking
    };
  }
}

// Export singleton instance
export const elevationService = new ElevationService();
