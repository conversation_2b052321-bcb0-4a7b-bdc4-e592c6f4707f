<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Viewshed Accuracy Demonstration</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background: #1a1a1a;
      color: #fff;
    }
    .container {
      max-width: 1000px;
      margin: 0 auto;
    }
    .comparison {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin: 20px 0;
    }
    .method {
      background: rgba(255, 255, 255, 0.1);
      padding: 20px;
      border-radius: 8px;
      border-left: 4px solid #4ade80;
    }
    .method.inaccurate {
      border-left-color: #ef4444;
    }
    .method.accurate {
      border-left-color: #4ade80;
    }
    .status {
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
    .success { background: rgba(74, 222, 128, 0.2); border: 1px solid #4ade80; }
    .error { background: rgba(239, 68, 68, 0.2); border: 1px solid #ef4444; }
    .warning { background: rgba(245, 158, 11, 0.2); border: 1px solid #f59e0b; }
    .info { background: rgba(59, 130, 246, 0.2); border: 1px solid #3b82f6; }
    button {
      background: #4ade80;
      color: #000;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover { background: #22c55e; }
    .result {
      background: rgba(0, 0, 0, 0.5);
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
      font-family: monospace;
      font-size: 12px;
      max-height: 300px;
      overflow-y: auto;
    }
    .elevation-data {
      background: rgba(0, 0, 0, 0.7);
      padding: 15px;
      border-radius: 4px;
      margin: 10px 0;
    }
    .real-data { border-left: 4px solid #4ade80; }
    .simulated-data { border-left: 4px solid #f59e0b; }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎯 Viewshed Accuracy: Real vs Simulated Data</h1>
    
    <div class="status warning">
      <strong>⚠️ IMPORTANT: Understanding Viewshed Accuracy</strong><br>
      This demonstration shows the difference between accurate viewshed analysis (using real elevation data) 
      and approximate analysis (using simulated data).
    </div>

    <div class="comparison">
      <div class="method accurate">
        <h2>✅ Accurate Method</h2>
        <h3>Real Elevation Data</h3>
        <ul>
          <li><strong>Data Source</strong>: Open-Meteo API (real DEM data)</li>
          <li><strong>Accuracy</strong>: ±1-3 meters</li>
          <li><strong>Coverage</strong>: Global</li>
          <li><strong>Update Frequency</strong>: Based on satellite data</li>
          <li><strong>Limitations</strong>: API rate limits, network dependency</li>
        </ul>
        <button onclick="testRealElevationData()">Test Real Elevation Data</button>
        <div id="real-data-result" class="result"></div>
      </div>

      <div class="method inaccurate">
        <h2>⚠️ Approximate Method</h2>
        <h3>Simulated Elevation Data</h3>
        <ul>
          <li><strong>Data Source</strong>: Geographic algorithms</li>
          <li><strong>Accuracy</strong>: ±100-1000 meters</li>
          <li><strong>Coverage</strong>: Global patterns only</li>
          <li><strong>Update Frequency</strong>: Static algorithms</li>
          <li><strong>Limitations</strong>: Not suitable for precise analysis</li>
        </ul>
        <button onclick="testSimulatedElevationData()">Test Simulated Data</button>
        <div id="simulated-data-result" class="result"></div>
      </div>
    </div>

    <div class="status info">
      <h2>🔍 Test Locations</h2>
      <p>We'll test both methods on known locations with verified elevations:</p>
      <ul>
        <li><strong>Mount Everest Base Camp</strong>: 5,364m (known elevation)</li>
        <li><strong>Death Valley</strong>: -86m (below sea level)</li>
        <li><strong>Denver, Colorado</strong>: 1,609m (mile-high city)</li>
        <li><strong>Amsterdam, Netherlands</strong>: -2m (below sea level)</li>
        <li><strong>Karachi, Pakistan</strong>: 8m (coastal city)</li>
      </ul>
    </div>

    <div class="comparison">
      <div class="elevation-data real-data">
        <h3>Real Elevation Results</h3>
        <div id="real-elevation-display">Click "Test Real Elevation Data" to see results</div>
      </div>
      
      <div class="elevation-data simulated-data">
        <h3>Simulated Elevation Results</h3>
        <div id="simulated-elevation-display">Click "Test Simulated Data" to see results</div>
      </div>
    </div>

    <div class="status success">
      <h2>✅ Recommendations for Accurate Viewshed Analysis</h2>
      <ol>
        <li><strong>Use Real APIs</strong>: Always try real elevation APIs first (Open-Meteo, USGS, etc.)</li>
        <li><strong>Handle Failures Gracefully</strong>: Fall back to simulation when APIs are unavailable</li>
        <li><strong>Cache Aggressively</strong>: Store real elevation data to reduce API calls</li>
        <li><strong>Validate Results</strong>: Check if elevation values are reasonable for the location</li>
        <li><strong>Inform Users</strong>: Clearly indicate when using simulated vs real data</li>
        <li><strong>Consider Local DEM Files</strong>: For critical applications, use local elevation databases</li>
      </ol>
    </div>

    <div class="status error">
      <h2>❌ Current Implementation Issues</h2>
      <p>Your concern is valid. The current implementation has these limitations:</p>
      <ul>
        <li><strong>Mixed Data Sources</strong>: Sometimes real, sometimes simulated</li>
        <li><strong>Unclear Indicators</strong>: Users don't know which data type is being used</li>
        <li><strong>Inconsistent Accuracy</strong>: Results vary depending on API availability</li>
        <li><strong>No Validation</strong>: No checks for reasonable elevation values</li>
      </ul>
    </div>

    <div class="status info">
      <h2>🛠️ Proposed Solution</h2>
      <p>To address your concerns, I recommend:</p>
      <ol>
        <li><strong>Clear Mode Selection</strong>: Let users choose "Real Data" vs "Simulation" modes</li>
        <li><strong>Data Source Indicators</strong>: Show which elevation source is being used</li>
        <li><strong>Accuracy Warnings</strong>: Alert users when using simulated data</li>
        <li><strong>Validation Checks</strong>: Verify elevation data makes sense for the location</li>
        <li><strong>Offline Capability</strong>: Provide high-quality simulation for offline use</li>
      </ol>
    </div>
  </div>

  <script>
    const testLocations = [
      { name: 'Mount Everest Base Camp', lat: 28.0026, lng: 86.8528, expected: 5364 },
      { name: 'Death Valley', lat: 36.5054, lng: -117.0794, expected: -86 },
      { name: 'Denver, Colorado', lat: 39.7392, lng: -104.9903, expected: 1609 },
      { name: 'Amsterdam, Netherlands', lat: 52.3676, lng: 4.9041, expected: -2 },
      { name: 'Karachi, Pakistan', lat: 24.8607, lng: 67.0011, expected: 8 }
    ];

    async function testRealElevationData() {
      const result = document.getElementById('real-data-result');
      const display = document.getElementById('real-elevation-display');
      
      result.innerHTML = 'Testing real elevation APIs...\n\n';
      display.innerHTML = '<strong>Testing in progress...</strong>';
      
      let displayHTML = '<strong>Real Elevation Data Results:</strong><br><br>';
      let totalAccuracy = 0;
      let successCount = 0;
      
      for (const location of testLocations) {
        try {
          result.innerHTML += `Testing ${location.name}...\n`;
          
          const response = await fetch(
            `https://api.open-meteo.com/v1/elevation?latitude=${location.lat}&longitude=${location.lng}`
          );
          
          if (response.ok) {
            const data = await response.json();
            const elevation = data.elevation ? Math.round(data.elevation[0]) : null;
            
            if (elevation !== null) {
              const accuracy = Math.abs(elevation - location.expected);
              const accuracyPercent = Math.max(0, 100 - (accuracy / Math.abs(location.expected)) * 100);
              
              result.innerHTML += `✅ ${location.name}: ${elevation}m (expected: ${location.expected}m, accuracy: ${accuracyPercent.toFixed(1)}%)\n`;
              
              displayHTML += `<div style="margin: 5px 0;">`;
              displayHTML += `<strong>${location.name}</strong><br>`;
              displayHTML += `Real API: <span style="color: #4ade80;">${elevation}m</span><br>`;
              displayHTML += `Expected: ${location.expected}m<br>`;
              displayHTML += `Accuracy: <span style="color: ${accuracyPercent > 90 ? '#4ade80' : accuracyPercent > 70 ? '#f59e0b' : '#ef4444'};">${accuracyPercent.toFixed(1)}%</span>`;
              displayHTML += `</div><br>`;
              
              totalAccuracy += accuracyPercent;
              successCount++;
            } else {
              result.innerHTML += `❌ ${location.name}: No elevation data returned\n`;
            }
          } else {
            result.innerHTML += `❌ ${location.name}: API failed (${response.status})\n`;
          }
        } catch (error) {
          result.innerHTML += `❌ ${location.name}: Error - ${error.message}\n`;
        }
      }
      
      const avgAccuracy = successCount > 0 ? totalAccuracy / successCount : 0;
      result.innerHTML += `\nOverall Accuracy: ${avgAccuracy.toFixed(1)}%\n`;
      result.innerHTML += `Successful API calls: ${successCount}/${testLocations.length}\n`;
      
      displayHTML += `<div style="border-top: 1px solid #4ade80; padding-top: 10px; margin-top: 10px;">`;
      displayHTML += `<strong>Overall Real Data Accuracy: <span style="color: #4ade80;">${avgAccuracy.toFixed(1)}%</span></strong>`;
      displayHTML += `</div>`;
      
      display.innerHTML = displayHTML;
      result.style.color = '#4ade80';
    }

    function testSimulatedElevationData() {
      const result = document.getElementById('simulated-data-result');
      const display = document.getElementById('simulated-elevation-display');
      
      result.innerHTML = 'Testing simulated elevation data...\n\n';
      display.innerHTML = '<strong>Testing in progress...</strong>';
      
      let displayHTML = '<strong>Simulated Elevation Data Results:</strong><br><br>';
      let totalAccuracy = 0;
      
      for (const location of testLocations) {
        const elevation = getRealisticElevation(location.lat, location.lng);
        const accuracy = Math.abs(elevation - location.expected);
        const accuracyPercent = Math.max(0, 100 - (accuracy / Math.abs(location.expected)) * 100);
        
        result.innerHTML += `${location.name}: ${elevation}m (expected: ${location.expected}m, accuracy: ${accuracyPercent.toFixed(1)}%)\n`;
        
        displayHTML += `<div style="margin: 5px 0;">`;
        displayHTML += `<strong>${location.name}</strong><br>`;
        displayHTML += `Simulated: <span style="color: #f59e0b;">${elevation}m</span><br>`;
        displayHTML += `Expected: ${location.expected}m<br>`;
        displayHTML += `Accuracy: <span style="color: ${accuracyPercent > 90 ? '#4ade80' : accuracyPercent > 70 ? '#f59e0b' : '#ef4444'};">${accuracyPercent.toFixed(1)}%</span>`;
        displayHTML += `</div><br>`;
        
        totalAccuracy += accuracyPercent;
      }
      
      const avgAccuracy = totalAccuracy / testLocations.length;
      result.innerHTML += `\nOverall Accuracy: ${avgAccuracy.toFixed(1)}%\n`;
      
      displayHTML += `<div style="border-top: 1px solid #f59e0b; padding-top: 10px; margin-top: 10px;">`;
      displayHTML += `<strong>Overall Simulated Accuracy: <span style="color: #f59e0b;">${avgAccuracy.toFixed(1)}%</span></strong>`;
      displayHTML += `</div>`;
      
      display.innerHTML = displayHTML;
      result.style.color = '#f59e0b';
    }

    function getRealisticElevation(latitude, longitude) {
      const mountainRanges = [
        { lat: 28, lng: 84, elevation: 8000, radius: 5 },
        { lat: 46, lng: 8, elevation: 4000, radius: 3 },
        { lat: 40, lng: -106, elevation: 4000, radius: 4 },
        { lat: -22, lng: -68, elevation: 6000, radius: 3 },
      ];
      
      let baseElevation = 0;
      
      for (const range of mountainRanges) {
        const distance = Math.sqrt(
          Math.pow(latitude - range.lat, 2) + Math.pow(longitude - range.lng, 2)
        );
        
        if (distance < range.radius) {
          const factor = 1 - (distance / range.radius);
          baseElevation = Math.max(baseElevation, range.elevation * factor);
        }
      }
      
      const coastalFactor = Math.min(
        Math.abs(latitude) / 90,
        Math.abs(Math.abs(longitude) - 180) / 180
      );
      
      const terrainNoise = 
        Math.sin(latitude * 0.1) * Math.cos(longitude * 0.1) * 200 +
        Math.sin(latitude * 0.05) * Math.cos(longitude * 0.05) * 500;
      
      return Math.round(Math.max(-200, baseElevation + terrainNoise * coastalFactor));
    }
  </script>
</body>
</html>
