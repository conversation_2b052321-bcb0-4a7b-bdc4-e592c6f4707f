<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no">
  <title>Enhanced Viewshed Analysis - iTrack Tactical Maps</title>
  <style>
    html, body {
      padding: 0;
      margin: 0;
      height: 100%;
      width: 100%;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #1a1a1a;
      color: #fff;
    }

    .container {
      display: flex;
      height: 100vh;
    }

    .sidebar {
      width: 300px;
      background: rgba(0, 0, 0, 0.9);
      padding: 20px;
      overflow-y: auto;
      border-right: 1px solid #333;
    }

    .map-container {
      flex: 1;
      position: relative;
      background: #2a2a2a;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .demo-map {
      width: 80%;
      height: 80%;
      background: linear-gradient(45deg, #4a5568, #2d3748);
      border: 2px solid #4a5568;
      border-radius: 8px;
      position: relative;
      overflow: hidden;
    }

    h1 {
      color: #ffd700;
      margin-bottom: 20px;
      font-size: 24px;
    }

    h2 {
      color: #4ade80;
      margin-top: 30px;
      margin-bottom: 15px;
      font-size: 18px;
    }

    .feature {
      background: rgba(255, 255, 255, 0.1);
      padding: 15px;
      margin: 10px 0;
      border-radius: 8px;
      border-left: 4px solid #ffd700;
    }

    .feature h3 {
      color: #ffd700;
      margin: 0 0 10px 0;
      font-size: 16px;
    }

    .feature p {
      margin: 5px 0;
      font-size: 14px;
      line-height: 1.4;
    }

    .comparison {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin: 20px 0;
    }

    .comparison-item {
      background: rgba(255, 255, 255, 0.05);
      padding: 15px;
      border-radius: 8px;
      border: 1px solid #4a5568;
    }

    .comparison-item h4 {
      color: #60a5fa;
      margin: 0 0 10px 0;
    }

    .comparison-item.enhanced {
      border-color: #4ade80;
    }

    .comparison-item.enhanced h4 {
      color: #4ade80;
    }

    .demo-viewshed {
      position: absolute;
      width: 120px;
      height: 120px;
      background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, rgba(255, 215, 0, 0.1) 70%, transparent 100%);
      border: 2px solid #ffd700;
      border-radius: 50%;
      top: 40%;
      left: 40%;
      animation: pulse 2s infinite;
    }

    .demo-observer {
      position: absolute;
      width: 12px;
      height: 12px;
      background: #ffd700;
      border: 2px solid #fff;
      border-radius: 50%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 10;
    }

    .demo-terrain {
      position: absolute;
      bottom: 20px;
      left: 20px;
      right: 20px;
      height: 60px;
      background: linear-gradient(to right, 
        rgba(139, 69, 19, 0.6) 0%, 
        rgba(34, 139, 34, 0.6) 30%, 
        rgba(139, 69, 19, 0.6) 60%, 
        rgba(34, 139, 34, 0.6) 100%);
      border-radius: 4px;
    }

    @keyframes pulse {
      0% { transform: scale(1); opacity: 0.7; }
      50% { transform: scale(1.1); opacity: 0.4; }
      100% { transform: scale(1); opacity: 0.7; }
    }

    .code-block {
      background: #1a1a1a;
      border: 1px solid #333;
      border-radius: 4px;
      padding: 10px;
      margin: 10px 0;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      overflow-x: auto;
    }

    .highlight {
      color: #4ade80;
      font-weight: bold;
    }

    .status {
      background: rgba(74, 222, 128, 0.2);
      border: 1px solid #4ade80;
      border-radius: 4px;
      padding: 10px;
      margin: 15px 0;
      text-align: center;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="sidebar">
      <h1>🎯 Enhanced Viewshed Analysis</h1>
      
      <div class="status">
        <strong>✅ Implementation Complete</strong><br>
        Terrain-based viewshed analysis now available
      </div>

      <h2>🚀 New Features</h2>
      
      <div class="feature">
        <h3>Terrain-Based Analysis</h3>
        <p>Real elevation data from multiple sources (Mapbox Terrain, Open Elevation API)</p>
        <p>True line-of-sight calculations considering terrain obstacles</p>
      </div>

      <div class="feature">
        <h3>Dual Analysis Modes</h3>
        <p><span class="highlight">Simple:</span> Fast geometric calculations</p>
        <p><span class="highlight">Terrain:</span> Accurate elevation-based analysis</p>
      </div>

      <div class="feature">
        <h3>Advanced Visualization</h3>
        <p><span class="highlight">Binary:</span> Visible/Hidden areas</p>
        <p><span class="highlight">Graduated:</span> Visible/Partial/Hidden with color coding</p>
      </div>

      <div class="feature">
        <h3>Configurable Parameters</h3>
        <p>Observer/target heights, view angles, distance, resolution</p>
        <p>Multiple elevation data sources</p>
      </div>

      <h2>📊 Comparison</h2>
      
      <div class="comparison">
        <div class="comparison-item">
          <h4>Previous (Simple)</h4>
          <p>• Geometric polygon generation</p>
          <p>• Simulated terrain variation</p>
          <p>• Fast but approximate</p>
        </div>
        
        <div class="comparison-item enhanced">
          <h4>Enhanced (Terrain)</h4>
          <p>• Real elevation data</p>
          <p>• True line-of-sight analysis</p>
          <p>• Accurate terrain consideration</p>
        </div>
      </div>

      <h2>🔧 Usage</h2>
      
      <div class="code-block">
// Access viewshed tool in tactical map
1. Click viewshed tool in toolbar
2. Choose analysis mode (Simple/Terrain)
3. Configure settings (height, distance, etc.)
4. Click map to place observers
5. View real-time analysis results
      </div>

      <div class="feature">
        <h3>Integration Benefits</h3>
        <p>✅ Maintains existing UI patterns</p>
        <p>✅ Backward compatible with simple mode</p>
        <p>✅ Performance optimized with caching</p>
        <p>✅ Multiple elevation data sources</p>
      </div>
    </div>

    <div class="map-container">
      <div class="demo-map">
        <div class="demo-viewshed"></div>
        <div class="demo-observer">👁</div>
        <div class="demo-terrain"></div>
        
        <div style="position: absolute; top: 10px; left: 10px; background: rgba(0,0,0,0.8); padding: 8px; border-radius: 4px; font-size: 12px;">
          <strong style="color: #ffd700;">Enhanced Viewshed Demo</strong><br>
          <span style="color: #4ade80;">🏔️ Terrain Analysis Active</span><br>
          Observer: 1.7m height<br>
          Range: 1000m<br>
          Elevation: Real DEM data
        </div>
      </div>
    </div>
  </div>
</body>
</html>
