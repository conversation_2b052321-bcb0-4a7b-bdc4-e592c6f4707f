<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Elevation Service Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background: #1a1a1a;
      color: #fff;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    .test-section {
      background: rgba(255, 255, 255, 0.1);
      padding: 20px;
      margin: 20px 0;
      border-radius: 8px;
      border-left: 4px solid #4ade80;
    }
    .result {
      background: rgba(0, 0, 0, 0.5);
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
      font-family: monospace;
      font-size: 12px;
    }
    button {
      background: #4ade80;
      color: #000;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover { background: #22c55e; }
    .status {
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
    .success { background: rgba(74, 222, 128, 0.2); border: 1px solid #4ade80; }
    .warning { background: rgba(245, 158, 11, 0.2); border: 1px solid #f59e0b; }
  </style>
</head>
<body>
  <div class="container">
    <h1>🏔️ Elevation Service Accuracy Test</h1>
    
    <div class="status warning">
      <strong>⚠️ Testing Real vs Simulated Elevation Data</strong><br>
      This test will show you the difference between real elevation APIs and simulated data.
    </div>

    <div class="test-section">
      <h2>Test 1: Real Elevation API</h2>
      <p>Testing with Open-Meteo elevation API (real DEM data):</p>
      <button onclick="testRealElevation()">Test Real Elevation API</button>
      <div id="real-elevation-result" class="result"></div>
    </div>

    <div class="test-section">
      <h2>Test 2: Simulated Elevation</h2>
      <p>Testing with geographic-based simulation:</p>
      <button onclick="testSimulatedElevation()">Test Simulated Elevation</button>
      <div id="simulated-elevation-result" class="result"></div>
    </div>

    <div class="test-section">
      <h2>Test 3: Line-of-Sight Accuracy</h2>
      <p>Testing line-of-sight calculations with both methods:</p>
      <button onclick="testLineOfSightAccuracy()">Test Line-of-Sight</button>
      <div id="los-accuracy-result" class="result"></div>
    </div>

    <div class="test-section">
      <h2>Recommendations</h2>
      <div class="status success">
        <strong>✅ For Accurate Viewshed Analysis:</strong>
        <ul>
          <li><strong>Use Real APIs</strong>: Open-Meteo or Open Elevation for actual terrain data</li>
          <li><strong>Handle API Failures</strong>: Graceful fallback to simulation when APIs are unavailable</li>
          <li><strong>Cache Results</strong>: Store elevation data to reduce API calls</li>
          <li><strong>Validate Results</strong>: Check if elevation values are reasonable for the location</li>
        </ul>
      </div>
    </div>
  </div>

  <script>
    // Test real elevation API
    async function testRealElevation() {
      const result = document.getElementById('real-elevation-result');
      result.innerHTML = 'Testing real elevation API...';
      
      const testLocations = [
        { name: 'Mount Everest Base Camp', lat: 28.0026, lng: 86.8528, expected: '~5300m' },
        { name: 'Death Valley', lat: 36.5054, lng: -117.0794, expected: '~-86m' },
        { name: 'Denver, Colorado', lat: 39.7392, lng: -104.9903, expected: '~1600m' },
        { name: 'Amsterdam, Netherlands', lat: 52.3676, lng: 4.9041, expected: '~-2m' },
        { name: 'Karachi, Pakistan', lat: 24.8607, lng: 67.0011, expected: '~8m' }
      ];
      
      let output = 'Real Elevation API Results:\n\n';
      
      for (const location of testLocations) {
        try {
          // Try Open-Meteo API
          const response = await fetch(
            `https://api.open-meteo.com/v1/elevation?latitude=${location.lat}&longitude=${location.lng}`
          );
          
          if (response.ok) {
            const data = await response.json();
            const elevation = data.elevation ? data.elevation[0] : 'N/A';
            output += `${location.name}:\n`;
            output += `  Real API: ${elevation}m\n`;
            output += `  Expected: ${location.expected}\n`;
            output += `  Status: ${response.ok ? '✅ Success' : '❌ Failed'}\n\n`;
          } else {
            throw new Error(`HTTP ${response.status}`);
          }
        } catch (error) {
          output += `${location.name}:\n`;
          output += `  Real API: ❌ Failed (${error.message})\n`;
          output += `  Expected: ${location.expected}\n\n`;
        }
      }
      
      result.innerHTML = output;
      result.style.color = '#4ade80';
    }

    // Test simulated elevation
    async function testSimulatedElevation() {
      const result = document.getElementById('simulated-elevation-result');
      result.innerHTML = 'Testing simulated elevation...';
      
      const testLocations = [
        { name: 'Mount Everest Base Camp', lat: 28.0026, lng: 86.8528, expected: '~5300m' },
        { name: 'Death Valley', lat: 36.5054, lng: -117.0794, expected: '~-86m' },
        { name: 'Denver, Colorado', lat: 39.7392, lng: -104.9903, expected: '~1600m' },
        { name: 'Amsterdam, Netherlands', lat: 52.3676, lng: 4.9041, expected: '~-2m' },
        { name: 'Karachi, Pakistan', lat: 24.8607, lng: 67.0011, expected: '~8m' }
      ];
      
      let output = 'Simulated Elevation Results:\n\n';
      
      for (const location of testLocations) {
        const elevation = getRealisticElevation(location.lat, location.lng);
        output += `${location.name}:\n`;
        output += `  Simulated: ${elevation}m\n`;
        output += `  Expected: ${location.expected}\n`;
        output += `  Accuracy: ${getAccuracyRating(elevation, location.expected)}\n\n`;
      }
      
      result.innerHTML = output;
      result.style.color = '#4ade80';
    }

    // Test line-of-sight accuracy
    async function testLineOfSightAccuracy() {
      const result = document.getElementById('los-accuracy-result');
      result.innerHTML = 'Testing line-of-sight accuracy...';
      
      // Test scenario: Observer on a hill looking at targets
      const observer = { lat: 30.3753, lng: 69.3451, height: 10, name: 'Hill Observer' };
      const targets = [
        { lat: 30.3853, lng: 69.3551, height: 2, name: 'Target 1 (nearby)' },
        { lat: 30.4053, lng: 69.3751, height: 2, name: 'Target 2 (medium)' },
        { lat: 30.4353, lng: 69.4051, height: 2, name: 'Target 3 (far)' }
      ];
      
      let output = 'Line-of-Sight Analysis:\n\n';
      output += `Observer: ${observer.name} at ${observer.lat}, ${observer.lng} (${observer.height}m height)\n\n`;
      
      for (const target of targets) {
        const distance = calculateDistance(observer.lat, observer.lng, target.lat, target.lng);
        const obsElevation = getRealisticElevation(observer.lat, observer.lng);
        const tarElevation = getRealisticElevation(target.lat, target.lng);
        
        // Simple line-of-sight calculation
        const heightDiff = (tarElevation + target.height) - (obsElevation + observer.height);
        const sightLine = heightDiff / distance * 1000; // slope per km
        const visible = sightLine < 0.1; // Visible if slope is gentle
        
        output += `${target.name}:\n`;
        output += `  Distance: ${Math.round(distance)}m\n`;
        output += `  Observer elevation: ${obsElevation}m + ${observer.height}m = ${obsElevation + observer.height}m\n`;
        output += `  Target elevation: ${tarElevation}m + ${target.height}m = ${tarElevation + target.height}m\n`;
        output += `  Height difference: ${heightDiff}m\n`;
        output += `  Sight line slope: ${sightLine.toFixed(3)} per km\n`;
        output += `  Visibility: ${visible ? '✅ VISIBLE' : '❌ BLOCKED'}\n\n`;
      }
      
      result.innerHTML = output;
      result.style.color = '#4ade80';
    }

    // Helper functions
    function getRealisticElevation(latitude, longitude) {
      // Major mountain ranges
      const mountainRanges = [
        { lat: 28, lng: 84, elevation: 8000, radius: 5 }, // Himalayas
        { lat: 46, lng: 8, elevation: 4000, radius: 3 },  // Alps
        { lat: 40, lng: -106, elevation: 4000, radius: 4 }, // Rocky Mountains
        { lat: -22, lng: -68, elevation: 6000, radius: 3 }, // Andes
      ];
      
      let baseElevation = 0;
      
      for (const range of mountainRanges) {
        const distance = Math.sqrt(
          Math.pow(latitude - range.lat, 2) + Math.pow(longitude - range.lng, 2)
        );
        
        if (distance < range.radius) {
          const factor = 1 - (distance / range.radius);
          baseElevation = Math.max(baseElevation, range.elevation * factor);
        }
      }
      
      const coastalFactor = Math.min(
        Math.abs(latitude) / 90,
        Math.abs(Math.abs(longitude) - 180) / 180
      );
      
      const terrainNoise = 
        Math.sin(latitude * 0.1) * Math.cos(longitude * 0.1) * 200 +
        Math.sin(latitude * 0.05) * Math.cos(longitude * 0.05) * 500;
      
      return Math.round(Math.max(0, baseElevation + terrainNoise * coastalFactor));
    }

    function calculateDistance(lat1, lng1, lat2, lng2) {
      const R = 6371000;
      const dLat = (lat2 - lat1) * Math.PI / 180;
      const dLng = (lng2 - lng1) * Math.PI / 180;
      const a = Math.sin(dLat/2) * Math.sin(dLat/2) + 
                Math.cos(lat1 * Math.PI/180) * Math.cos(lat2 * Math.PI/180) * 
                Math.sin(dLng/2) * Math.sin(dLng/2);
      return R * 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    }

    function getAccuracyRating(simulated, expected) {
      const expectedNum = parseInt(expected.replace(/[^\d-]/g, ''));
      const diff = Math.abs(simulated - expectedNum);
      
      if (diff < 100) return '🎯 Excellent';
      if (diff < 500) return '✅ Good';
      if (diff < 1000) return '⚠️ Fair';
      return '❌ Poor';
    }
  </script>
</body>
</html>
